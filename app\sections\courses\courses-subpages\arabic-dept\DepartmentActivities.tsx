"use client";

import EventsDisplay from "@/app/components/EventsDisplay";
import React from "react";

// Define the interface for the props
interface Activity {
  _id: string;
  title: string;
  date: string;
  description?: string;
  image?: string; // Image URL
}

// Interface expected by EventsDisplay (assuming it requires image and description)
interface Event {
  _id?: string;
  image: string;
  title: string;
  date: string;
  description: string;
}

interface DepartmentActivitiesProps {
  activities: Activity[];
}

const DepartmentActivities: React.FC<DepartmentActivitiesProps> = ({ activities }) => {

  // Filter activities to ensure they have both image and description
  const validEvents: Event[] = activities
    .filter((activity): activity is Activity & { image: string; description: string } =>
      typeof activity.image === 'string' && activity.image.trim() !== '' &&
      typeof activity.description === 'string' && activity.description.trim() !== ''
    )
    // Map to ensure the structure matches Event if needed (though filter might be enough)
    .map(activity => ({
      _id: activity._id,
      title: activity.title,
      date: activity.date,
      image: activity.image, // Now guaranteed to be a string
      description: activity.description, // Now guaranteed to be a string
    }));

  // Optional: Handle case where there are no *valid* activities
  if (!validEvents || validEvents.length === 0) {
    return (
      <section className="py-10 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-semibold text-center mb-4">Department Activities</h2>
          <p className="text-center text-gray-600">No activities with complete details available at the moment.</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-10 bg-white">
      <EventsDisplay
        events={validEvents} // Use the filtered and typed array
        title="Department Activities"
        description="Stay updated with the latest events and activities happening in the department."
        enableNavigation={true} // Enable navigation for department activities
      />
    </section>
  );
};

export default DepartmentActivities;
