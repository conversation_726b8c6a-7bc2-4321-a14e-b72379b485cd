"use client";

import React from "react";
import Gallery3D from "@/app/components/Gallery3d";
import HeroSection from "@/app/components/HeroSectionDeptActivity";
import "./Activity.css";

interface Activity {
  _id: string;
  title: string;
  date: string;
  description?: string;
  image?: string;
}

interface ActivityPageClientProps {
  activity: Activity;
  departmentName?: string;
}

const ActivityPageClient: React.FC<ActivityPageClientProps> = ({
  activity,
  departmentName
}) => {
  return (
    <div className="min-h-screen">
      {/* Background Image with Overlay - matching site pattern */}
      <div
        className="fixed inset-0 bg-cover bg-center -z-10"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>
      </div>

      <HeroSection
        activity={activity}
        departmentName={departmentName}
      />
      <Gallery3D
        title={`${activity.title} Gallery`}
        description={`Capturing the memorable moments of ${activity.title}`}
      />
    </div>
  );
};

export default ActivityPageClient;
