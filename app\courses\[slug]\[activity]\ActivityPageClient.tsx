"use client";

import React from "react";
import Gallery3D from "@/app/components/Gallery3d";
import HeroSection from "@/app/components/HeroSectionDeptActivity";
import "./Activity.css";

interface Activity {
  _id: string;
  title: string;
  date: string;
  description?: string;
  image?: string;
}

interface ActivityPageClientProps {
  activity: Activity;
  departmentName?: string;
}

const ActivityPageClient: React.FC<ActivityPageClientProps> = ({ 
  activity, 
  departmentName 
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-950 via-green-900 to-slate-900">
      <HeroSection
        activity={activity}
        departmentName={departmentName}
      />
      <Gallery3D
        title={`${activity.title} Gallery`}
        description={`Capturing the memorable moments of ${activity.title}`}
      />
    </div>
  );
};

export default ActivityPageClient;
