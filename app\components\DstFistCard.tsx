import React from "react";
import Link from "next/link";

const DstFistCard = () => {
  return (
    <div className="px-4 md:px-12 py-8 md:py-8">
      {/* Card with shadow */}
      <div className="bg-white rounded-lg shadow-[0_0_24px_rgba(0,0,0,0.5)] p-4 md:p-12">
        <h2 className="text-2xl font-bold text-[#002E00] text-center mb-8">
          DST - FIST
        </h2>

        <div className="space-y-6">
          <div className="px-2">
            <h3 className="font-bold text-lg mb-2 text-[#002E00]">About DST - FIST</h3>
            <ul className="list-disc pl-6 space-y-4 text-[#565656]">
              <li>
                <p>Our college has been selected under the DST - FIST Scheme for "Strengthening of Research Infrastructure" by the Department of Science and Technology (DST), Govt. of India. DST has sanctioned Rs. 110.00 lakhs (Rupees One Crore Ten lakhs only), Rs. 60.5 lakhs (Rupees Sixty lakhs fifty thousand) has been already received as first instalment. During this academic year (2021-2022), Rs. 45.25 lakhs (Rupees Forty-four lakhs twenty-five thousand only) has been released for the second instalment. Under "DST - FIST Programme" Fourier Transform Infrared Spectrometer, Fourier Transform Raman Spectrometer, Atomic Absorption Spectrophotometer and Analytical Cum Semi Preparative HPLC, Gradient HPLC instruments have been purchased and installed using the DST-FIST Fund. Further construction of e-learning classroom and providing networking facilities under this scheme are in progress.</p>
              </li>
            </ul>
          </div>

          <div >
            <h3 className="font-bold text-lg mb-4 text-[#002E00]">DST - FIST Annual Reports</h3>
            <div className="flex flex-col md:flex-row flex-wrap gap-2 md:px-8">
              <Link href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Research/DST%E2%80%93FIST/02_DST-FIST-Annual-Report-2019-2021-2-33.pdf" target="_blank" rel="noopener noreferrer" className="inline-block bg-[#002E00] text-white text-center py-4 px-6 rounded-md hover:bg-[#003E00] transition-colors mr-4 mb-2">
                DST - FIST Annual Report 2019-2021
              </Link>
              <Link href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Research/DST%E2%80%93FIST/FIST-REPORT-2021-2022.pdf" target="_blank" rel="noopener noreferrer" className="inline-block bg-[#002E00] text-white text-center py-4 px-6 rounded-md hover:bg-[#003E00] transition-colors mr-4 mb-2">
                DST - FIST Annual Report 2021-2022
              </Link>
              <Link href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Research/DST%E2%80%93FIST/FIST-REPORT-2022-2023.pdf" target="_blank" rel="noopener noreferrer" className="inline-block bg-[#002E00] text-white text-center py-4 px-6 rounded-md hover:bg-[#003E00] transition-colors mr-4 mb-2">
                DST - FIST Annual Report 2022-2023
              </Link>
            </div>
          </div>

          <div >
            <h3 className="font-bold text-lg mb-4 text-[#002E00]">DST - FIST Sanction Orders</h3>
            <div className="flex flex-col md:flex-row flex-wrap gap-2 md:px-8">
              <Link href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Research/DST%E2%80%93FIST/03_DST_1_Sanction_order.pdf" target="_blank" rel="noopener noreferrer" className="inline-block bg-[#002E00] text-white text-center py-4 px-8 md:px-16 rounded-md hover:bg-[#003E00] transition-colors mr-4 mb-2">
                DST - FIST Sanction Order 1
              </Link>
              <Link href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Research/DST%E2%80%93FIST/04_DST_2_sanction_order.pdf" target="_blank" rel="noopener noreferrer" className="inline-block bg-[#002E00] text-white text-center py-4 px-8 md:px-16 rounded-md hover:bg-[#003E00] transition-colors mr-4 mb-2">
                DST - FIST Sanction Order 2
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DstFistCard;
