import React from "react";
import CommitteeTable from "@/app/components/CommitteeTable";

const ResearchEthicsCommittee = () => {
  // Sample data for Research Ethics Committee
  const internalMembers = [
    {
      id: 1,
      name: "Dr. <PERSON><PERSON> <PERSON><PERSON>",
      designation: "Management Representative and Secretary & Correspondent",
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>",
      designation: "Management Representative and Treasurer",
    },
    {
      id: 3,
      name: "Dr. <PERSON><PERSON>",
      designation: "Management Representative and Assistant Secretary",
    },
    {
      id: 4,
      name: "Dr. <PERSON><PERSON><PERSON><PERSON>",
      designation: "Member and Honorary Director",
    },
    {
      id: 5,
      name: "Dr. <PERSON><PERSON> <PERSON> <PERSON>",
      designation: "Principal & Chairperson",
    },
    {
      id: 6,
      name: "Dr. <PERSON><PERSON>",
      designation: "Vice Principal",
    },
    {
      id: 7,
      name: "Dr. <PERSON><PERSON><PERSON>",
      designation: "Additional Vice Principal",
    },
    {
      id: 8,
      name: "Dr. <PERSON><PERSON> <PERSON><PERSON>",
      designation: "Additional Vice Principal",
    },
    {
      id: 9,
      name: "<PERSON><PERSON><PERSON>",
      designation: "Bursar & HOD of English",
    },
    {
      id: 10,
      name: "<PERSON><PERSON> <PERSON><PERSON>",
      designation: "Dean of Research and Consultancy(M)",
    },
    {
      id: 11,
      name: "Dr. <PERSON>. <PERSON><PERSON><PERSON><PERSON>",
      designation: "Dean of Arts (M) & HOD of Hindi",
    },
    {
      id: 12,
      name: "Dr. A. R. <PERSON> Shanavas",
      designation: "Dean of Science (M) & Associate Professor of CS",
    },
    {
      id: 13,
      name: "Dr. A. <PERSON>eetha",
      designation: "Dean of Research (W) & Assistant Professor of N&D",
    },
    {
      id: 14,
      name: "Dr. A.S. Haja Hameed",
      designation: "Coordinator of JIIC, IPR CELL and Associate Professor of Physics",
    },
    {
      id: 15,
      name: "Dr. M. Salahudeen",
      designation: "Teacher Representative and Assistant Professor of Zoology",
    },
    {
      id: 16,
      name: "Dr. A. Abul Hussain",
      designation: "Library Representative and Librarian",
    },
    {
      id: 17,
      name: "Mr. M. Ragamath Ali",
      designation: "Teacher Representative and Assistant Professor of IT",
    },
  ];

  const externalMembers = [
    {
      id: 1,
      name: "Dr. V. Rajesh Kannan",
      designation: "Professor of Microbiology, Bharathidasan University, Tiruchirappalli",
    },
    {
      id: 2,
      name: "Dr. S. Manivannan",
      designation: "Associate Professor, Department of Physics, National Institute of Technology, Tiruchirappalli – 620015, Tamil Nadu.",
    }
  ];

  return (
    <CommitteeTable
      title="Research Ethics Committee"
      internalMembers={internalMembers}
      externalMembers={externalMembers}
    />
  );
};

export default ResearchEthicsCommittee;
