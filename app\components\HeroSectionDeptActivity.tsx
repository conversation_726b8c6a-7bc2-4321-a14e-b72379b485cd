import { Download, Images, InfoIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import Image from "next/image";

interface Activity {
  _id: string;
  title: string;
  date: string;
  description?: string;
  image?: string;
}

interface HeroSectionProps {
  activity: Activity;
  departmentName?: string;
}

export default function HeroSection({ activity, departmentName }: HeroSectionProps) {
  const scrollToGallery = () => {
    document.getElementById('gallery')?.scrollIntoView({ behavior: 'smooth' });
  };

  // Parse the date to extract day, month, year
  const parseDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      day: date.getDate().toString().padStart(2, '0'),
      month: (date.getMonth() + 1).toString().padStart(2, '0'),
      year: date.getFullYear().toString()
    };
  };

  const { day, month, year } = parseDate(activity.date);

  return (
    <section className="pt-32 min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-600 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center">

          {/* Poster Section */}
          <motion.div
            className="order-2 lg:order-1"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl blur-lg opacity-75 group-hover:opacity-100 transition-opacity"></div>
              <div className="relative bg-white rounded-2xl shadow-2xl p-8 transform group-hover:scale-105 transition-transform duration-300">
                <Image
                  src={activity.image || "https://images.unsplash.com/photo-1523580494863-6f3031224c94?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600"}
                  alt={activity.title}
                  width={800}
                  height={600}
                  className="w-full h-auto rounded-lg shadow-lg"
                  priority
                />
                <div className="absolute top-4 right-4">
                  <Button className="bg-green-600 hover:bg-green-700 text-white">
                    <Download className="w-4 h-4 mr-2" />
                    Download PDF
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Event Details Section */}
          <motion.div
            className="order-1 lg:order-2 text-center lg:text-left"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
          >
            <div className="mb-6">
              <span className="inline-block px-4 py-2 bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-full text-green-300 text-sm font-medium border border-green-500/30">
                {departmentName || "Department Activity"}
              </span>
            </div>

            <h1 className="text-4xl md:text-6xl font-playfair font-bold mb-6">
              <span className="text-white">{activity.title.toUpperCase()}</span>
            </h1>

            <div className="flex flex-col sm:flex-row items-center lg:items-start lg:flex-col xl:flex-row gap-6 mb-8">
              <div className="flex items-center space-x-4 bg-white/10 rounded-2xl px-6 py-4 backdrop-blur-sm">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">{day}</div>
                  <div className="text-sm text-green-300">Day</div>
                </div>
                <div className="w-px h-12 bg-white/20"></div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">{month}</div>
                  <div className="text-sm text-green-300">Month</div>
                </div>
                <div className="w-px h-12 bg-white/20"></div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">{year}</div>
                  <div className="text-sm text-green-300">Year</div>
                </div>
              </div>
            </div>

            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              {activity.description || activity.title}
              <br />
              <span className="text-green-300">Celebrating Academic Excellence</span>
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={scrollToGallery}
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-4 h-auto font-semibold transform hover:scale-105 transition-all"
              >
                <Images className="w-5 h-5 mr-2" />
                View Gallery
              </Button>
              <Button
                variant="outline"
                className="border-white/30 text-white hover:bg-white/10 px-8 py-4 h-auto font-semibold"
              >
                <InfoIcon className="w-5 h-5 mr-2" />
                Event Details
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}