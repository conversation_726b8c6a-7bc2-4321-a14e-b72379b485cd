import Navbar from "@/components/Navbar";
import React from "react";
import { client } from "@/sanity/lib/client";
import Footer from "@/app/sections/Footer";
import ActivityPageClient from "./ActivityPageClient";

export async function generateStaticParams() {
  // Get all courses and their activities to generate static paths
  const coursesWithActivities = await client.fetch<{
    slug: string;
    activities: { title: string }[];
  }[]>(
    `*[_type == "courses" && defined(slug.current)]{
      "slug": slug.current,
      "activities": departmentActivities[]->{ title }
    }`
  );

  const paths: { slug: string; activity: string }[] = [];

  coursesWithActivities.forEach((course) => {
    if (course.activities) {
      course.activities.forEach((activity) => {
        const activitySlug = activity.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '')
          .replace(/^-+|-+$/g, '');

        paths.push({
          slug: course.slug,
          activity: activitySlug,
        });
      });
    }
  });

  return paths;
}

const ActivityPage = async ({
  params
}: {
  params: { slug: string; activity: string }
}) => {
  // Get course details and find the specific activity
  const courseDetails = await client.fetch(
    `*[_type == "courses" && slug.current == $slug][0]{
      departmentName,
      departmentActivities[]->{
        _id,
        title,
        date,
        description,
        image
      }
    }`,
    { slug: params.slug }
  );

  // Find the activity that matches the slug
  const activity = courseDetails?.departmentActivities?.find((act: any) => {
    const activitySlug = act.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '')
      .replace(/^-+|-+$/g, '');
    return activitySlug === params.activity;
  });

  // If activity not found, show 404-like message
  if (!activity) {
    return (
      <>
        <Navbar fixed={true} border={true} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">Activity Not Found</h1>
            <p className="text-gray-600">The requested activity could not be found.</p>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative px-4 pt-36 pb-16 text-white bg-cover bg-center"
        style={{ backgroundImage: "url('/bg_image.webp')" }}
      >
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>
        <div className="relative container max-w-7xl mx-auto z-10">
          <h1 className="font-ramilas text-4xl md:text-5xl font-bold mb-4">
            {activity.title}
          </h1>
          <p className="text-lg opacity-90">
            {courseDetails?.departmentName || "Department Activity"}
          </p>
        </div>
      </header>

      <main className="min-h-screen bg-white">
        <ActivityPageClient
          activity={activity}
          departmentName={courseDetails?.departmentName}
        />
      </main>

      <Footer />
    </>
  );
};

export default ActivityPage;
