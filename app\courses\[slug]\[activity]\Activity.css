@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 140 25% 8%;
  --foreground: 120 15% 95%;
  --muted: 140 20% 15%;
  --muted-foreground: 120 10% 70%;
  --popover: 140 25% 8%;
  --popover-foreground: 120 15% 95%;
  --card: 140 25% 8%;
  --card-foreground: 120 15% 95%;
  --border: 140 20% 15%;
  --input: 140 20% 15%;
  --primary: 140 65% 45%;
  --primary-foreground: 140 25% 8%;
  --secondary: 140 20% 15%;
  --secondary-foreground: 120 15% 95%;
  --accent: 140 20% 15%;
  --accent-foreground: 120 15% 95%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 120 15% 95%;
  --ring: 140 65% 45%;
  --radius: 0.5rem;
}

.dark {
  --background: 140 25% 8%;
  --foreground: 120 15% 95%;
  --muted: 140 20% 15%;
  --muted-foreground: 120 10% 70%;
  --popover: 140 25% 8%;
  --popover-foreground: 120 15% 95%;
  --card: 140 25% 8%;
  --card-foreground: 120 15% 95%;
  --border: 140 20% 15%;
  --input: 140 20% 15%;
  --primary: 140 65% 45%;
  --primary-foreground: 140 25% 8%;
  --secondary: 140 20% 15%;
  --secondary-foreground: 120 15% 95%;
  --accent: 140 20% 15%;
  --accent-foreground: 120 15% 95%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 120 15% 95%;
  --ring: 140 65% 45%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

.font-playfair {
  font-family: 'Playfair Display', serif;
}

.gradient-text {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.glass-effect {
  backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

#gallery-container {
  perspective: 1000px;
}

.gallery-image {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}