import React from "react";
import Image from "next/image";

interface TeamMemberProps {
  image: string;
  name: string;
  title: string;
  description: string;
  currentPosition?: string;
  email?: string;
}

const TeamMemberCard: React.FC<TeamMemberProps> = ({
  image,
  name,
  title,
  description,
  currentPosition,
  email,
}) => {
  return (
    <div className="flex flex-col md:flex-row overflow-hidden mb-3 sm:mb-4 px-2 sm:px-4 ">
      <div className="w-full md:w-1/4 lg:w-1/5 h-74 md:h-64 sm:h-64 md:h-auto relative">
        <Image
          src={image}
          alt={name}
          width={300}
          height={300}
          className="object-cover object-center w-full h-full rounded-lg"
        />
      </div>
      <div className="w-full md:w-3/4 lg:w-4/5 p-4 sm:p-6">
        <h3 className="text-xl sm:text-2xl font-bold text-[#002E00] mb-2">{title}</h3>
        <p className="text-sm sm:text-base text-[#555555] mb-2">
          <span className="font-semibold">{name}, </span>
          {description}
        </p>
        {currentPosition && (
          <p className="text-sm sm:text-base text-[#555555] mb-2">
            <span className="font-semibold">Currently serving as: </span>
            {currentPosition}
          </p>
        )}
        {email && (
          <p className="text-sm sm:text-base text-[#555555]">
            <span className="font-semibold">Email: </span>
            {email}
          </p>
        )}
      </div>
    </div>
  );
};

const ResearchTeam = () => {
  const teamMembers: TeamMemberProps[] = [
    {
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Research/CentreOfResearch/d1.jpg",
      name: "Dr. A. Shajahan",
      title: "Dean of Research and Consultancy",
      description: "Associate Professor of Botany, Jamal Mohamed College, Tiruchirappalli.",
      currentPosition: "Dean of Research and Consultancy",
      email: "<EMAIL>",
    },
    {
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Research/CentreOfResearch/d2.jpg",
      title: "Research Advisor",
      name: "Dr. B. Padmanaban",
      description: "FPPAI, FSBA., Former Principal Scientist(Agril.Entomology) Agricultural Research, Service of ICAR, Tiruchirapalli, INDIA",
      currentPosition: "Research Advisor",
      email: "<EMAIL>",
    },
    {
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Research/CentreOfResearch/d-new-1.jpg",
      title: "Research Advisor",
      name: "Dr. S.M.M.S. Maricar",
      description: "Former Addl. General Manager, Govt. of India, Min. Defence, Heavy Alloy Penetrator Project, Tiruchirappalli - 620 025",
      currentPosition: "Research Advisor",
      email: "<EMAIL>",
    },
    {
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Research/CentreOfResearch/d3.jpg",
      title: "Coordinator: Tamil Innovation and Incubation Centre (TIIC & IPFC)",
      name: "Dr. A. S. Haja Hameed",
      description: "Associate Professor of Physics, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Coordinator of Tamil Innovation and Incubation Centre",
      email: "<EMAIL>",
    },
    {
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Research/CentreOfResearch/d5.jpg",
      title: "Dean of Research (Women)",
      name: "Dr. A. Sangeetha",
      description: "Assistant Professor of N&D, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Dean of Research for Women's Section",
      email: "<EMAIL>",
    },
    {
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Research/CentreOfResearch/d-new-2.jpg",
      title: "Coordinator JMIC",
      name: "Dr. M. Purushothaman",
      description: "Associate Professor of Chemistry, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Coordinator of Jamal Mohamed Innovation Centre",
      email: "<EMAIL>",
    },
    {
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Research/CentreOfResearch/d6.jpg",
      title: "Research Core Committee Member",
      name: "Dr. F.M. Mashood Ahamed",
      description: "Assistant Professor of Chemistry, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Member of Research Core Committee",
      email: "<EMAIL>",
    },
    {
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Research/CentreOfResearch/d7.jpg",
      title: "Junior Assistant",
      name: "Mr. R. Nirmal",
      description: "B.A., Junior Assistant, Research & Consultancy, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Junior Assistant in Research & Consultancy Department",
      email: "<EMAIL>",
    },
  ];

  return (
    <section className="py-8 sm:py-12 md:py-16 bg-[#EBEBEB]">
      <h2 className="text-2xl sm:text-3xl font-bold text-center text-[#002E00] mb-4 sm:mb-8 px-4">
        Controller of Examinations
      </h2>
      <div className="container max-w-7xl mx-auto px-2 sm:px-4">
        <div className="space-y-3 sm:space-y-4">
          {teamMembers.map((member, index) => (
            <TeamMemberCard
              key={index}
              image={member.image}
              name={member.name}
              title={member.title}
              description={member.description}
              currentPosition={member.currentPosition}
              email={member.email}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ResearchTeam;
