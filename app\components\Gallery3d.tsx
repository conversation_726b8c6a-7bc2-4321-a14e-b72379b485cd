import { useEffect, useRef, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import Image from "next/image";

interface GalleryImage {
  url: string;
  alt: string;
}

interface Gallery3DProps {
  images?: GalleryImage[];
  title?: string;
  description?: string;
}

// Placeholder images for maintaining 3D design when insufficient images
const placeholderImages = [
  { url: "https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Placeholder image 1" },
  { url: "https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Placeholder image 2" },
  { url: "https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Placeholder image 3" },
  { url: "https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Placeholder image 4" },
  { url: "https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Placeholder image 5" }
];

// Gallery images - using stock photos for college graduation/convocation
const galleryImages = [
  // College graduation ceremony (6 images)
  { url: "https://images.unsplash.com/photo-1523050854058-8df90110c9d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Graduation ceremony with students in caps and gowns" },
  { url: "https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Graduates throwing caps in celebration" },
  { url: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Student receiving diploma on stage" },
  { url: "https://images.unsplash.com/photo-1622495704404-436d3dc7cb6a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Graduates celebrating with families" },
  { url: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Graduation stage and podium setup" },
  { url: "https://images.unsplash.com/photo-1523580494863-6f3031224c94?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Academic procession with faculty" },

  // Academic convocation events (4 images)
  { url: "https://images.unsplash.com/photo-1562774053-701939374585?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "University campus architecture" },
  { url: "https://images.unsplash.com/photo-1576267423445-b2e0074d68a4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Academic convocation hall" },
  { url: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Medal ceremony with officials" },
  { url: "https://images.unsplash.com/photo-1577896851231-70ef18881754?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Group photo of graduates" },

  // University campus scenes (3 images)
  { url: "https://images.unsplash.com/photo-1541829070764-84a7d30dd3f3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "University campus courtyard" },
  { url: "https://images.unsplash.com/photo-1498243691581-b145c3f54a5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Campus library interior" },
  { url: "https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "University campus at evening" }
];

export default function Gallery3D({
  images,
  title = "Event Photo Gallery",
  description = "Capturing the memorable moments of our event"
}: Gallery3DProps = {}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Prepare display images with placeholder support
  const prepareDisplayImages = () => {
    const providedImages = images && images.length > 0 ? images : galleryImages;
    const minImagesFor3D = 5; // Minimum images needed for good 3D effect

    if (providedImages.length >= minImagesFor3D) {
      return {
        displayImages: providedImages,
        originalCount: providedImages.length,
        hasPlaceholders: false
      };
    }

    // Add placeholders to maintain 3D design
    const neededPlaceholders = minImagesFor3D - providedImages.length;
    const placeholdersToAdd = placeholderImages.slice(0, neededPlaceholders);

    return {
      displayImages: [...providedImages, ...placeholdersToAdd],
      originalCount: providedImages.length,
      hasPlaceholders: true
    };
  };

  const { displayImages, originalCount, hasPlaceholders } = prepareDisplayImages();
  const hasNavigation = originalCount > 1;

  useEffect(() => {
    // Simulate loading delay for images
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const nextImage = () => {
    if (hasNavigation) {
      setCurrentIndex((prev) => (prev + 1) % originalCount);
    }
  };

  const prevImage = () => {
    if (hasNavigation) {
      setCurrentIndex((prev) => (prev - 1 + originalCount) % originalCount);
    }
  };

  const goToImage = (index: number) => {
    if (hasNavigation && index < originalCount) {
      setCurrentIndex(index);
    }
  };

  // Calculate rotation for 3D effect with placeholder support
  const getImageStyle = (index: number) => {
    const totalImages = displayImages.length;
    const angleStep = 360 / totalImages;
    const currentAngle = -currentIndex * angleStep;
    const imageAngle = index * angleStep + currentAngle;

    // Normalize angle for better calculations
    const normalizedAngle = ((imageAngle % 360) + 540) % 360 - 180;
    const absAngle = Math.abs(normalizedAngle);

    // Check if this is a placeholder image
    const isPlaceholder = index >= originalCount;
    const isCurrent = index === currentIndex;
    const isVisible = absAngle <= 120; // Show images within 120 degrees

    // Calculate depth and scale
    const baseTranslateZ = 320;
    const translateZ = isCurrent ? baseTranslateZ + 40 : baseTranslateZ;
    const scale = isCurrent ? 1.15 : isVisible ? 0.95 : 0.8;

    // Calculate opacity - placeholders are more transparent
    let opacity = 1;
    if (isPlaceholder) {
      opacity = 0.3; // Very transparent for placeholders
    } else if (isCurrent) {
      opacity = 1;
    } else if (isVisible) {
      opacity = 0.8;
    } else {
      opacity = 0.4;
    }

    return {
      transform: `
        rotateY(${imageAngle}deg)
        translateZ(${translateZ}px)
        scale(${scale})
      `,
      opacity,
      zIndex: isCurrent ? 20 : isVisible ? 10 : 5,
      pointerEvents: (isPlaceholder ? 'none' : 'auto') as 'none' | 'auto',
    };
  };

  if (isLoading) {
    return (
      <section id="gallery" className="py-24 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold text-white mb-6">
              {title}
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              {description}
            </p>
          </div>
          <div className="h-96 md:h-[500px] flex items-center justify-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="gallery" className="py-24 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-playfair font-bold text-white mb-6">
            {title}
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            {description}
          </p>
        </motion.div>

        {/* 3D Gallery Container */}
        <div className="relative">
          <div
            ref={containerRef}
            className="h-96 md:h-[500px] relative"
            style={{ perspective: '1000px' }}
          >
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={{
                transformStyle: 'preserve-3d',
                transition: 'transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
              }}
            >
              {displayImages.map((image, index) => (
                <motion.div
                  key={index}
                  className="absolute w-64 h-48 md:w-80 md:h-60 cursor-pointer"
                  style={getImageStyle(index)}
                  onClick={() => goToImage(index)}
                  whileHover={{ scale: index === currentIndex ? 1.15 : 0.95 }}
                  transition={{ duration: 0.3 }}
                >
                  <Image
                    src={image.url}
                    alt={image.alt}
                    width={320}
                    height={240}
                    className={`w-full h-full object-cover rounded-2xl shadow-2xl border-2 ${
                      index >= originalCount
                        ? 'grayscale opacity-50 border-white/20'
                        : index === currentIndex
                          ? 'border-green-400'
                          : 'border-white/30'
                    }`}
                    loading="lazy"
                  />
                  {/* Show description for current original image */}
                  {index === currentIndex && index < originalCount && (
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-2xl flex items-end justify-center p-4">
                      <p className="text-white text-sm text-center font-medium drop-shadow-lg">
                        {image.alt}
                      </p>
                    </div>
                  )}
                  {/* Show placeholder indicator */}
                  {index >= originalCount && (
                    <div className="absolute inset-0 bg-black/40 rounded-2xl flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-8 h-8 border-2 border-white/50 rounded-full mb-2 mx-auto"></div>
                        <p className="text-white text-xs font-medium opacity-70">
                          Placeholder
                        </p>
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>

          {/* Navigation Controls - Only show if more than one original image */}
          {hasNavigation && (
            <div className="flex justify-center items-center mt-8 space-x-6">
              <Button
                onClick={prevImage}
                variant="outline"
                size="icon"
                className="w-14 h-14 bg-black/50 hover:bg-black/70 rounded-full border-white/30 text-white shadow-xl"
              >
                <ChevronLeft className="w-6 h-6" />
              </Button>

              <div className="flex space-x-3">
                {/* Only show dots for original images */}
                {Array.from({ length: originalCount }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToImage(index)}
                    className={`w-4 h-4 rounded-full transition-all shadow-lg ${
                      index === currentIndex
                        ? 'bg-green-500 ring-2 ring-green-300'
                        : 'bg-white/50 hover:bg-white/70'
                    }`}
                  />
                ))}
              </div>

              <Button
                onClick={nextImage}
                variant="outline"
                size="icon"
                className="w-14 h-14 bg-black/50 hover:bg-black/70 rounded-full border-white/30 text-white shadow-xl"
              >
                <ChevronRight className="w-6 h-6" />
              </Button>
            </div>
          )}

          {/* Image Counter - Only show if more than one original image */}
          {hasNavigation && (
            <div className="text-center mt-6">
              <span className="inline-block px-6 py-3 bg-black/50 rounded-full text-white border border-white/20 shadow-xl font-medium">
                {currentIndex + 1} / {originalCount}
              </span>
            </div>
          )}

          {/* Single Image Message */}
          {!hasNavigation && (
            <div className="text-center mt-6">
              <span className="inline-block px-6 py-3 bg-black/50 rounded-full text-white border border-white/20 shadow-xl font-medium">
                Featured Image
              </span>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}